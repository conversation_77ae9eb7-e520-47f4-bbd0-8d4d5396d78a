/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable no-param-reassign */
const sassResourcesLoader = require('craco-sass-resources-loader');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const ExternalTemplateRemotesPlugin = require('external-remotes-plugin');
const { whenDev, whenProd } = require('@craco/craco');
const { dependencies } = require('./package.json');

module.exports = {
  typescript: {
    enableTypeChecking: true,
  },
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: [
          'node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss',
          'src/assets/styles/variables.scss',
        ],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {
      const simVersion = process.env.SIM_VERSION;
      const accountVersion = process.env.ACCOUNT_VERSION;
      const ratePlanVersion = process.env.RATE_PLAN_VERSION;
      const billingVersion = process.env.BILLING_VERSION;
      const automationVersion = process.env.AUTOMATION_VERSION;

      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'container',
          ...whenDev(() => ({
            remotes: {
              header: 'header@http://localhost:3001/remoteEntry.js',
              // spogRatePlans: 'spogRatePlans@http://localhost:3002/remoteEntry.js',
              // spogBilling: 'spogBilling@http://localhost:3003/remoteEntry.js',
              // spogSimManagement: 'spogSimManagement@http://localhost:3003/remoteEntry.js',
              spogAccountManagement: 'spogAccountManagement@http://localhost:3005/remoteEntry.js',
              // simAutomation: 'simAutomation@http://localhost:3006/remoteEntry.js',
            },
          })),
          ...whenProd(() => ({
            remotes: {
              header: 'header@[nv2HeaderUrl]/remoteEntry.js',
              spogRatePlans: `spogRatePlans@/rate-plans-app/remoteEntry.js?v=${ratePlanVersion}`,
              spogBilling: `spogBilling@/billing-app/remoteEntry.js?v=${billingVersion}`,
              spogSimManagement: `spogSimManagement@/sim-management-app/remoteEntry.js?v=${simVersion}`,
              spogAccountManagement: `spogAccountManagement@/account-management-app/remoteEntry.js?v=${accountVersion}`,
              simAutomation: `simAutomation@/sim-automation-app/remoteEntry.js?v=${automationVersion}`,
            },
          })),
          shared: {
            ...dependencies,
            react: {
              eager: true,
              singleton: true,
              requiredVersion: dependencies.react,
            },
            'react-router-dom': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['react-router-dom'],
            },
            'react-dom': {
              eager: true,
              singleton: true,
              requiredVersion: dependencies['react-dom'],
            },
          },
        }),
        new ExternalTemplateRemotesPlugin(),
      ];

      webpackConfig.output.publicPath = 'auto';

      webpackConfig.output = {
        ...webpackConfig.output,
        publicPath: '/',
        clean: true,
      };

      return webpackConfig;
    },
  },
};
