export enum OrganizationTypes {
  DISTRIBUTOR = 'DISTRIBUTOR',
  CLIENT = 'CLIENT',
}

export interface IOrganization {
  id: number
  name: string
  parent_id: string | null
  type: OrganizationTypes
}

export interface IUser {
  email: string
  firstName: string
  id: string
  lastName: string
  organization: IOrganization
}

export interface AppConfig {
  apiUrl: string;
  coreApiUrl: string;
  keycloakLogoutUrl: string;
  hubspotMeetingUrl: string;
  themeName: string;
  // subDomain: string;
}
