// import { AppContext } from 'AppContextProvider';
// import React, { useContext, useEffect } from 'react';
// import Loader from 'shared/Loader';

// const HomePage = () => {
//   const useAppContext = useContext(AppContext);
//   useEffect(() => {
//     const subDomain = useAppContext?.config?.subDomain ?? 'app.';
//     window.location.assign(window.location.origin.replace(subDomain, ''));
//   }, [useAppContext]);

//   return (
//     <div data-testid="container-home">
//       <Loader />
//     </div>
//   );
// };

// export default HomePage;

import React, { useEffect } from 'react';
import Loader from 'shared/Loader';

const HomePage = () => {
  useEffect(() => {
    window.location.assign(window.location.origin.replace('app.', ''));
  }, []);

  return (
    <div data-testid="container-home">
      <Loader />
    </div>
  );
};

export default HomePage;
