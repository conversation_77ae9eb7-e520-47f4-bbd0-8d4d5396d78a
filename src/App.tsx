import { ThemeProvider } from '@mui/material';
import themeConfig from '@nv2/nv2-pkg-js-theme/src/components/configs/themeConfig';
import setFavicon from '@nv2/nv2-pkg-js-theme/src/components/setFavicon';
import theme from '@nv2/nv2-pkg-js-theme/src/components/theme/theme';
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import GTMWrapper, { useGTM } from '@nv2/nv2-pkg-js-shared-components/lib/GTMWrapper';
import AppContextProvider from 'AppContextProvider';
import { ConfigSettingsService, CookiesService, HTTPService } from 'core/services';
import { coreAxios } from 'core/services/HTTPService';
import React, { FC, useEffect, useState } from 'react';
import { useCookies } from 'react-cookie';
import BaseLayout from 'layouts/BaseLayout';
import { Route, Routes, useNavigate } from 'react-router-dom';
import { AppConfig } from 'user.model';
import GTM_ID from 'core/utilities/constants';
import './assets/styles/reset.scss';

const App: FC = () => {
  // TODO temporary fix. When find solution how to combine mui from 2 remote components remove it.
  const [themeName, setThemeName] = useState('bt');
  const { pushToDataLayer } = useGTM();
  const [isInitialized, setIsInitialized] = useState(false);
  const navigate = useNavigate();
  const [cookies] = useCookies();

  const getThemeConfig = (name: string) => {
    const theme = themeConfig[name];
    if (theme) {
      return theme;
    }

    console.warn(`Theme '${name}' not found in themeConfig. Falling back to 'bt' theme.`);
    return themeConfig['bt'] || {};
  };

  const currentTheme = getThemeConfig(themeName);
  const [config, setConfig] = useState<AppConfig>({
    apiUrl: '',
    coreApiUrl: '',
    keycloakLogoutUrl: '',
    hubspotMeetingUrl: '', // HubSpot meeting URL
    themeName: '',
    subDomain: '',
  });

  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const getProdLogOutUrl = (keycloakLogOutUrl: string) => {
    const keycloakLogoutUrlFull = `${keycloakLogOutUrl}?redirect_uri=${window.location.origin}`;

    return `/oauth2/sign_out?rd=${encodeURIComponent(keycloakLogoutUrlFull)}`;
  };

  const logOut = async (url: string) => {
    window.location.replace(url);
  };

  const getLogoutUrl = (url: string) => (isDevelopmentMode
    ? process.env.REACT_APP_LOGOUT_URL
    : getProdLogOutUrl(url));

  const redirectToLogin = () => {
    const currentPathname = window.location.href;
    const entryPointUrl = `${process.env.REACT_APP_LOGIN_URL}?entryPath=${currentPathname}`;

    navigate(entryPointUrl);
  };

  const authenticateForDevelopmentMode = () => {
    const cookieDoesNotHaveAccessToken = !CookiesService.getAccessToken(cookies).value;

    if (cookieDoesNotHaveAccessToken) {
      redirectToLogin();
    }

    HTTPService.setAccessToken(coreAxios, cookies);
  };

  const setupInitialData = async () => {
    const { data } = await ConfigSettingsService.getAppVariables();

    const logoutUrl = getLogoutUrl(data?.themeName === 'bt' ? data?.coreUiUrl : data.keycloakLogoutUrl);

    if (isDevelopmentMode) {
      authenticateForDevelopmentMode();
    }

    HTTPService.setDefaultGlobalConfig(coreAxios, data.apiUrl);

    HTTPService.setCorsError(coreAxios, logOut, logoutUrl);

    // Use fallback theme name if the requested theme doesn't exist
    const safeThemeName = data.themeName && themeConfig[data.themeName] ? data.themeName : 'bt';

    try {
      setFavicon(safeThemeName);
    } catch (error) {
      console.error('Error setting favicon:', error);
      // Try with fallback theme
      try {
        setFavicon('bt');
      } catch (fallbackError) {
        console.error('Error setting favicon with fallback theme:', fallbackError);
      }
    }

    setThemeName(safeThemeName);
    pushToDataLayer({ env: data.env });
    setConfig(data);
    setIsInitialized(true);
  };

  const handleGTMError = (error: Error, context: string) => {
    // eslint-disable-next-line no-console
    console.error(`GTM Error: ${context}`, error);
  };

  useEffect(() => {
    setupInitialData();
  }, []);

  return (
    <ThemeProvider
      theme={theme(currentTheme)}
    >
      <AppContextProvider isInitialized={isInitialized} themeValue={{ themeName, currentTheme }} config={config}>
        <GTMWrapper
          gtmId={GTM_ID}
          onError={handleGTMError}
        />
        <Routes>
          <Route path="*" element={<BaseLayout />} />
        </Routes>
      </AppContextProvider>
    </ThemeProvider>
  );
};

export default App;
