import React, {
  createContext, ReactNode, useEffect, useState, useContext, useMemo,
} from 'react';
import getBrandColors from '@nv2/nv2-pkg-js-theme/src/components/getBrandColors';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { useGTM } from '@nv2/nv2-pkg-js-shared-components/lib/GTMWrapper';
import { AppConfig, IUser } from './user.model';
import { getUser } from './api.service';

interface IAppContext {
  user: IUser | undefined,
  isLoading: boolean,
  config: AppConfig,
  primaryColor: string,
  secondaryColor: string,
  themeName: string,
  getBrandColors: (color: string) => ({ [index: number]: string }),
}

interface IAppContextProvider {
  children: ReactNode | ReactNode[],
  isInitialized: boolean,
  themeValue: {
    themeName: string,
    currentTheme: Record<string, string>
  }
  // config: AppConfig,
}

export const AppContext = createContext<Partial<IAppContext>>({});

export const useAppContext = () => useContext(AppContext);

const AppContextProvider = ({
  children, isInitialized, themeValue,
}: IAppContextProvider) => {
  const { themeName, currentTheme } = themeValue;
  const [isLoading, setIsLoading] = useState(true);
  const { pushToDataLayer } = useGTM();
  const [user, setUser] = useState<IUser | undefined>();

  const getCurrentThemeColors = (color: string) => getBrandColors(color, themeName);

  const themeVariables = {
    primaryColor: currentTheme?.primaryColor || '#000000', // Fallback to black
    secondaryColor: currentTheme?.secondaryColor || '#666666', // Fallback to gray
    getBrandColors: getCurrentThemeColors,
    themeName,
  };

  useEffect(() => {
    if (!isInitialized) return;

    (async () => {
      try {
        setIsLoading(true);
        const { data } = await getUser();
        setUser(data);
        pushToDataLayer({ ...data });
        localStorage.setItem('userDetails', JSON.stringify(data));
      } finally {
        setIsLoading(false);
      }
    })();
  }, [isInitialized]);

  const value = useMemo(() => ({
    user,
    isLoading,
    // config,
    ...themeValue,
    ...themeVariables,
  }), [user]);

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

export default AppContextProvider;
