/* eslint-disable @typescript-eslint/no-unused-vars */
import PageNotFound from '@nv2/nv2-pkg-js-shared-components/lib/PageNotFound';
import paths from 'core/configs/paths';
import getPageTitle from 'core/utilities/getPageTitle';
import BillingPage from 'pages/BillingPage';
import React, { FC, useEffect } from 'react';
import { Route, Routes, useParams } from 'react-router-dom';
import HomePage from 'pages/HomePage';
import SimAutomationPage from 'pages/SimAutomationPage';
import RatePlansPage from 'pages/RatePlansPage';
import SimManagementPage from 'pages/SimManagementPage';
import AccountManagementPage from 'pages/AccountManagement/AccountManagement';
import { useAppContext } from './AppContextProvider';

const PageNotFoundWrapper = () => {
  const { primaryColor, getBrandColors } = useAppContext();

  return (
    <PageNotFound
      primaryColor={primaryColor}
      getCurrentThemeColors={getBrandColors}
    />
  );
};

const AppRoutes: FC = () => {
  const params = useParams();

  useEffect(() => {
    const title = document.querySelector('title');
    if (title) {
      title.innerHTML = getPageTitle(params['*']);
    }
  }, [params]);

  return (
    <Routes>
      <Route path={paths.home} element={<HomePage />} />
      {/* <Route path={paths.simAutomation} element={<SimAutomationPage />} /> */}
      {/* <Route path={paths.billing} element={<BillingPage />} /> */}
      {/* <Route path={paths.ratePlans} element={<RatePlansPage />} /> */}
      {/* <Route path={paths.simManagement} element={<SimManagementPage />} /> */}
      <Route path={paths.accountManagement} element={<AccountManagementPage />} />
      <Route path={paths.notFound} element={<PageNotFoundWrapper />} />
      <Route path="*" element={<PageNotFoundWrapper />} />
    </Routes>
  );
};

export default AppRoutes;
