import React, { FC } from 'react';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import Header from 'components/Header';
import AppRoutes from 'AppRoutes';

import './BaseLayout.scss';

const BaseLayout: FC = () => (
  <div className="container-layout" style={{ background: styles.lightColor100 }}>
    <Header />
    <div className="container-layout__inner">
      <AppRoutes />
    </div>
  </div>
);

export default BaseLayout;
