# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.htm).

### Unreleased

### Added

### Changed

### Removed

### Fixed

## Released
0.2.25 08-08-2025


### Added

### Changed
[SIM-3346] order-id_and_onHOld_status

### Removed

### Fixed

## Released
0.2.24 01-08-2025

### Added
[SIM-3311] Integrated GTM component in container.

### Changed
[SIM-3311] Changed localstorage key name.
[SIM-3311] Version changed.

### Removed

### Fixed

## Released
0.2.23 30-07-2025

### Added

### Changed
[SIM-3225] - common auditlog changes

### Removed

### Fixed

## Released
0.2.22 30-07-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3519] Something-went-wrong-message-is-shown-for-non-real-SIM

## Released
0.2.21 28-07-2025

### Added

### Changed

### Removed

### Fixed

## Released
0.2.20 24-07-2025

### Added
[SIM-3186] identify-efferts-for-map

### Changed

### Removed

### Fixed

## Released
0.2.19 07-07-2025

### Added

### Changed
[SIM-3262] - changed all auditlog api path name
[SIM-3279] - Need to allow zero fees value in ‘SIM fee' field for Fixed Rate Plan Creation

### Removed

### Fixed

## Released
0.2.18 30-06-2025

### Added

### Changed
[SIM-3256] sub task demo changes for sim order
### Removed

### Fixed

## Released
0.2.17 25-06-2025

### Added

### Changed
[SIM-3121] - Add Order SIM tab
### Removed

### Fixed

## Released
0.2.16 23-06-2025

### Added

### Changed
[SIM] - SIM and Account release

### Removed

### Fixed

## Released
0.2.15 23-06-2025

### Added

### Changed
[SIM-3234] - Minor release SIM and Account

### Removed

### Fixed

## Released
0.2.14 20-06-2025

### Added

### Changed
[SIM] - changed sim and account version release 4.0

### Removed

### Fixed

## Released
0.2.13 19-06-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3210] - Billing Patch Release

## Released
0.2.12 18-06-2025

### Added
[SIM-3210]  - Billing date bug solved

### Changed

### Removed

### Fixed

## Released
0.2.11 12-06-2025

### Added
[SIM] - [FE] Billing and SIM Management Release

### Changed

### Removed

### Fixed

## Released
0.2.10 10-06-2025

### Added
[SIMM-2989] - [FE] Integration of voice/SMS definitions is required
[SIM-3059] - Bulk operation feature removed approutes permission 

### Changed

### Removed

### Fixed

## Released
0.2.9 10-06-2025

### Added

### Changed
[SIM-2133] - Update account version
### Removed

### Fixed

## Released
0.2.8 02-06-2025

### Added
[SIM-3154]  - Keyclaock changes of route

### Changed

### Removed

### Fixed

## Released
0.2.7 29-05-2025

### Added

### Changed
[SIM-3046] - SIM management and Account Management release

### Removed

### Fixed

## Released
0.2.6 20-05-2025

### Added
[SIM-3080]- [UI] Integrate UI changes for Respective APIs

### Changed

### Removed

### Fixed

## Released
0.2.5 01-05-2025

### Added
[SIM-3031] - Need to fix toast issue for success message and UI of View Automation in account management

### Changed

### Removed

### Fixed

## Released
0.2.4 29-04-2025

### Added

### Changed
[SIM-2825] - node and npm update and setup the intial versions.

### Removed

### Fixed
[SIM-2936] - trivy fixed
[SIM-2936] - Fix main page

## Released
0.2.3 22-07-2024

### Added
[SPOG-1813]-ui-enable-code-coverage-to-all-micro-front-ends
Add maxWorkers in test script in package.json file

### Changed

### Removed

### Fixed
