#!/bin/sh -xe

###############################################################################
# Environment variables assignment and re-assignment
# Use the below link to find out variables set by deployment:
# https://gitlabnv2.flyaps.com/devops/deploy/-/tree/master/environments
# PLEASE UPDATE THE FOLLOWING VARS ACCORDINGLY TO YOUR APPLICATION REQUIREMENTS

export ENV=${ENV:-"spogdev"}
export DOMAIN=${DOMAIN:-"dev.spogconnected.com"}
export AUTH_HOST=${AUTH_HOST:-"sso.dev.spogconnected.com"}
export AUTH_REALM=${AUTH_REALM:-"dev-connected-platform"}
export UI_DEFAULT_THEME_NAME=${UI_DEFAULT_THEME_NAME:-""}
export CORE_HEADER_UI_URL=${CORE_HEADER_UI_URL:-"https://${DOMAIN}/header"}
export INDEX_LOCATION="index.html"
export UI_HOME_PAGE=${UI_HOME_PAGE:-"app."}

###############################################################################
# Service management commands implemented as functions start here
# Configure them as needed, but do NOT modify their names
###############################################################################

no_effect() {
	mcmd="${1}"
	echo "Management command '${mcmd}' has no effect for this service"
	return 0
}

init_db() {
	no_effect init_db
}

populate_db() {
	no_effect populate_db
}

migrate_db() {
	no_effect migrate_db
}

rollback_db() {
	no_effect rollback_db
}

purge_db() {
	no_effect purge_db
}

run_cmd() {
	/bin/sh -ec "${@}"
}

create_config() {
	mkdir -p settings
	cat <<- EOF > settings/config.json
	{
	"env": "${ENV}",
	"coreApiUrl": "https://${DOMAIN}/v1",
	"apiUrl": "https://${DOMAIN}/v1",
	"coreUiUrl": "https://${DOMAIN}",
	"keycloakLogoutUrl": "https://${AUTH_HOST}/auth/realms/${AUTH_REALM}/protocol/openid-connect/logout",
	"themeName": "${UI_DEFAULT_THEME_NAME}",
	"subdomain": "${UI_HOME_PAGE}",
	}
	EOF
}

insert_core_header_location() {
	if test -f ${INDEX_LOCATION}; then
		INSERT_LINE="""<script type="text/javascript" async>window.nv2HeaderUrl=\"${CORE_HEADER_UI_URL}\";</script>"""
		sed -i "s~</body>~${INSERT_LINE}</body>~" "${INDEX_LOCATION}"
	fi
}

start_service() {
	create_config
	insert_core_header_location
	nginx -g 'daemon off;'
}

###############################################################################
# Service management commands end here, no updates below this line
###############################################################################

manage_script="${0}"
if test "${#}" = "0"; then
	echo "ERROR: ${manage_script} requires at least one argument as management command"
	exit 1
fi
manage_command="${1}"
shift 1

case "${manage_command}" in
	start_service|run_cmd|init_db|populate_db|migrate_db|rollback_db|purge_db|create_config)
		"${manage_command}" "${@}"
		;;
	*)
		echo "ERROR: unknown management command '${manage_command}'"
		exit 1
		;;
esac
